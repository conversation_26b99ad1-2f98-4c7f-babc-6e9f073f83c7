import { useEffect, useState } from "react";
import { FaPlus } from "react-icons/fa";
import { IoAdd, IoCloseSharp } from "react-icons/io5";
import { Link, useNavigate, useParams } from "react-router-dom";
import { api } from "../../service/api";
import { AlertModal } from "../AlertModal";

interface StaffUser {
	id: number;
	username: string;
	userType: "staff" | "admin";
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
	staff?: {
		id: number;
		comissionRate: string;
	};
}

interface ClientModel {
	id: number;
	address: string;
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
}

interface Garment {
	id: number;
	name: string;
	refcode: string;
	price: string;
	size: string;
	color: string;
}

interface Order {
	id: number;
	customer_id: number;
	staff_id: number;
	deadline: string;
	date: string;
	status: string;
	garments: Array<{
		id: number;
		quantity: number;
	}>;
}

interface UpdateOrderProps {
	$isntupdate: boolean;
	$isupdate: boolean;
	refreshTrigger: boolean;
}

export function CadPedido({
	$isupdate,
	$isntupdate,
	refreshTrigger,
}: UpdateOrderProps) {
	const [orderToUpdate, setOrderToUpdate] = useState<Order[]>([]);

	const [customers, setCustomers] = useState<ClientModel[]>([]);
	const [customerSelected, setCustomerSelected] = useState<number | null>(0);

	const [staffs, setStaffs] = useState<StaffUser[]>([]);
	const [staffSelected, setStaffSelected] = useState<number | null>(0);

	const [deadline, setDeadline] = useState("");

	const [garments, setGarments] = useState<Garment[]>([]);
	const [garmentSelected, setGarmentSelected] = useState<number | null>(0);
	const [garmentToSend, setGarmentToSend] = useState<
		{ id: number; quantity: number }[]
	>([]);

	const { order_id } = useParams();

	const [loading, setLoading] = useState(true);

	const navigate = useNavigate();

	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	const formatDate = (date: Date): string => {
		const day = String(date.getDate()).padStart(2, "0");
		const month = String(date.getMonth() + 1).padStart(2, "0");
		const year = date.getFullYear();
		return `${day}-${month}-${year}`;
	};

	const formatTime = (date: Date): string => {
		const hours = String(date.getHours()).padStart(2, "0");
		const minutes = String(date.getMinutes()).padStart(2, "0");
		const seconds = String(date.getSeconds()).padStart(2, "0");
		return `${hours}:${minutes}:${seconds}`;
	};

	const now = new Date();

	const parsedDeadline = (() => {
		const [year, month, day] = deadline.split("-").map(Number);
		return new Date(year, month - 1, day);
	})();

	const setMinDate = (): void => {
		const dateInput = document.getElementById(
			"deadline",
		) as HTMLInputElement | null;

		if (!dateInput) {
			return;
		}

		const today = new Date().toISOString().split("T")[0];
		dateInput.min = today;
	};

	setMinDate();

	function handleAddGarmentToOrder() {
		const selectedGarment = garments.find(
			(garment) => garment.id === garmentSelected,
		);
		const quantityInput = Number.parseInt(
			(document.getElementById("parts-number") as HTMLInputElement).value,
		);

		if (!selectedGarment || !quantityInput || quantityInput <= 0) {
			setAlertMessage("Selecione um modelo e insira uma quantidade válida.");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		setGarmentToSend((prev) => {
			const garmentExists = prev.find((item) => item.id === selectedGarment.id);
			if (garmentExists) {
				return prev.map((item) =>
					item.id === selectedGarment.id
						? { ...item, quantity: item.quantity + quantityInput }
						: item,
				);
			}
			return [...prev, { id: selectedGarment.id, quantity: quantityInput }];
		});

		(document.getElementById("model-ref") as HTMLInputElement).value = "none";
		(document.getElementById("parts-number") as HTMLInputElement).value = "";
	}

	// biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
	useEffect(() => {
		async function fetchData() {
			try {
				const [customersRes, staffRes, garmentRes, orderRes] =
					await Promise.all([
						api.get("/customers"),
						api.get("/users"),
						api.get("/garments"),
						order_id ? api.get(`/orders/${order_id}`) : Promise.resolve(null),
					]);

				setCustomers(customersRes.data);
				setStaffs(staffRes.data);
				setGarments(garmentRes.data);

				if (orderRes) {
					const orderData = orderRes.data;

					const [day, month, year] = orderData.deadline.split("-");
					const formattedDeadline = `${year}-${month}-${day}`;

					setOrderToUpdate(orderData);
					setCustomerSelected(orderData.customer_id);
					setStaffSelected(orderData.staff_id);
					setDeadline(formattedDeadline);
					setGarmentToSend(
						orderData.garments.map(
							(garment: { id: number; quantity: number }) => ({
								id: garment.id,
								quantity: garment.quantity,
							}),
						),
					);
				}
			} catch (error) {
				console.error("Erro ao carregar dados:", error);
			} finally {
				setLoading(false);
			}
		}

		fetchData();
	}, [order_id, refreshTrigger]);

	if (loading) {
		return <p>Carregando dados...</p>;
	}

	async function handleSubmit() {
		if (customerSelected === 0 || null) {
			setAlertMessage("Selecione um cliente...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (staffSelected === 0 || null) {
			setAlertMessage("Selecione um funcionário...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (deadline === "") {
			setAlertMessage("Selecione uma data de entrega...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (garmentToSend.length === 0 || null) {
			setAlertMessage("Adicione pelo menos uma peça ao pedido...");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			if ($isupdate) {
				const data = {
					customer_id: customerSelected,
					staff_id: staffSelected,
					deadline: formatDate(parsedDeadline),
					garments: garmentToSend,
				};

				await api.patch(`/orders/${order_id}`, data);

				setAlertMessage("Pedido atualizado com sucesso!");
				setAlertType("success");
				setAlertCallback(() => () => navigate("/lista-de-pedidos"));
				setIsAlertModalOpen(true);

				return;
			}

			if ($isntupdate) {
				const data = {
					customer_id: customerSelected,
					staff_id: staffSelected,
					date: formatDate(now),
					time: formatTime(now),
					deadline: formatDate(parsedDeadline),
					status: "Fila de Espera",
					garments: garmentToSend,
				};

				await api.post("/orders", data);

				setAlertMessage("Pedido cadastrado com sucesso!");
				setAlertType("success");
				setAlertCallback(() => () => navigate("/lista-de-pedidos"));
				setIsAlertModalOpen(true);

				setCustomerSelected(0);
				setStaffSelected(0);
				setDeadline("");
				setGarmentToSend([]);
			}
		} catch (error) {
			setAlertMessage(
				$isntupdate ? "Erro ao cadastrar pedido." : "Erro ao atualizar pedido.",
			);
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	return (
		<>
			<div className="container mx-auto px-4 justify-items-center">
				<form
					id="cad-pedido"
					className="text-sm sm:text-base flex items-center"
				>
					<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4 w-72">
						<div className="input-wrapper flex flex-col">
							<div className="flex items-center justify-between mb-1">
								<label htmlFor="client-name" className="mb-1">
									Nome do Cliente
								</label>

								<Link
									to={"/novo-cliente"}
									className="flex items-center gap-2 border pl-1 py-0.5"
								>
									Novo <IoAdd className="text-2xl" />
								</Link>
							</div>
							<select
								id="client-name"
								className="text-sm border border-gray-300 rounded px-2 py-1 w-64 sm:w-full"
								value={customerSelected || 0}
								onChange={(e) => setCustomerSelected(Number(e.target.value))}
							>
								<option value={0}>Escolha o nome do cliente...</option>
								{customers.map((customer) => (
									<option key={customer.id} value={customer.id}>
										{customer.person.fullname}
									</option>
								))}
							</select>
						</div>
						<div className="line-wrapper flex flex-col sm:flex-row justify-between gap-2 sm:gap-4">
							<div className="input-wrapper flex flex-col flex-grow">
								<label htmlFor="seller-name" className="mb-1">
									Nome do Vendedor:
								</label>
								<select
									id="seller-name"
									className={`text-sm border border-gray-300 rounded px-2 py-1 w-64 ${$isupdate ? "bg-gray-100 cursor-not-allowed" : ""
										}`}
									value={staffSelected || 0}
									onChange={(e) => setStaffSelected(Number(e.target.value))}
									disabled={$isupdate} // Torna o campo não modificável
								>
									<option value={0}>Escolha o nome do vendedor...</option>
									{staffs.map((staff) => (
										<option key={staff.id} value={staff.id}>
											{staff.person.fullname}
										</option>
									))}
								</select>
							</div>

							<div className="input-wrapper flex flex-col flex-grow">
								<label htmlFor="deadline" className="mb-1">
									Prazo:
								</label>
								<input
									type="date"
									id="deadline"
									className="text-sm border border-gray-300 rounded px-2 py-1 w-64"
									value={deadline}
									onChange={(e) => setDeadline(e.target.value)}
								/>
							</div>
						</div>
						<div className="line-wrapper flex justify-between gap-1 w-64 sm:w-full sm:gap-4">
							<div className="input-wrapper flex flex-col flex-grow w-1/2">
								<label htmlFor="model-ref" className="mb-1">
									Código do Modelo:
								</label>
								<select
									id="model-ref"
									className="text-sm border border-gray-300 rounded px-2 py-1"
									onChange={(e) => setGarmentSelected(Number(e.target.value))}
								>
									<option id="defaultOption" value="none">
										Escolha o modelo...
									</option>
									{garments.map((garment) => (
										<option key={garment.id} value={garment.id}>
											{garment.refcode}
										</option>
									))}
								</select>
							</div>

							<div className="input-wrapper flex flex-col flex-grow w-1/2">
								<label htmlFor="parts-number" className="mb-1 truncate">
									Quantidade de peças:
								</label>
								<input
									type="number"
									id="parts-number"
									min={0}
									className="text-sm border border-gray-300 rounded px-2 py-1"
									placeholder="Ex: 20"
								/>
							</div>
						</div>
						<button
							type="button"
							className="border border-gray-300 bg-gray-300 rounded px-2 py-1 flex items-center justify-center gap-2 text-lg"
							onClick={handleAddGarmentToOrder}
						>
							<FaPlus /> Adicionar ao pedido
						</button>
						<div className="toSend input-wrapper flex flex-col place-content-evenly items-center border border-gray-300 rounded-md py-1">
							{garmentToSend.map((item) => {
								const garment = garments.find((g) => g.id === item.id);
								return (
									<div key={item.id} className="flex items-center gap-4 py-1">
										<div className="flex flex-col sm:flex-row sm:gap-4">
											<span>Modelo: {garment?.refcode}</span>
											<span>Quantidade: {item.quantity}</span>
										</div>

										<IoCloseSharp
											className="text-red-600 cursor-pointer"
											onClick={() =>
												setGarmentToSend((prev) =>
													prev.filter((g) => g.id !== item.id),
												)
											}
										/>
									</div>
								);
							})}
						</div>
						<button
							type="button"
							className="font-semibold bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
							onClick={(e) => {
								e.preventDefault();
								handleSubmit();
							}}
						>
							{$isntupdate && "Cadastrar Pedido"}
							{$isupdate && "Atualizar Pedido"}
						</button>{" "}
					</fieldset>
				</form>
			</div>
			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => {
					setIsAlertModalOpen(false);
					if (alertType === "success" && alertCallback) {
						alertCallback();
					}
				}}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</>
	);
}
