import { PiNotePencilBold } from "react-icons/pi";

import { useCallback, useEffect, useState } from "react";
import { IoClose } from "react-icons/io5";
import { Link } from "react-router-dom";
import { AlertModal } from "../../../components/AlertModal";
import { Footer } from "../../../components/Footer";
import { Header } from "../../../components/Header";
import { api } from "../../../service/api";

interface Garment {
	id: number;
	name: string;
	refcode: string;
	price: string | number;
	size: string;
	color: string;
	in_stock: number;
}

export function StockControl() {
	const [garments, setGarments] = useState<Garment[]>([]);
	const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
	const [selectedGarment, setSelectedGarment] = useState<Garment | null>(null);
	const [activeTab, setActiveTab] = useState<"modelos" | "materia-prima">(
		"modelos",
	);

	const [stockToAdd, setStockToAdd] = useState<number>(0);
	const [selectedStock, setSelectedStock] = useState<number | null>(null);
	const [finalStock, setFinalStock] = useState<number | null>(null);
	const [searchTerm, setSearchTerm] = useState<string>("");

	const [isAlertModalOpen, setIsAlertModalOpen] = useState(false);
	const [alertType, setAlertType] = useState<"success" | "error" | "confirm">(
		"success",
	);
	const [alertMessage, setAlertMessage] = useState("");
	const [alertCallback, setAlertCallback] = useState<(() => void) | null>(null);

	// Função para recarregar a lista de modelos
	const reloadGarments = useCallback(async () => {
		try {
			const response = await api.get("/garments");
			const allGarments = response.data;

			// Se o termo de busca tem menos de 3 caracteres, mostra todos os modelos
			// Se tem 3 ou mais caracteres, aplica o filtro de busca
			if (searchTerm.trim().length >= 3) {
				const filteredGarments = allGarments.filter((garment: Garment) =>
					garment.name.toLowerCase().includes(searchTerm.toLowerCase()),
				);
				setGarments(filteredGarments);
			} else {
				setGarments(allGarments);
			}
		} catch (error) {
			console.error("Erro ao buscar peças:", error);
		}
	}, [searchTerm]);

	const openModal = (garment: Garment): void => {
		setIsModalOpen(true);
		setSelectedGarment(garment);
		setSelectedStock(garment.in_stock);
		setFinalStock(garment.in_stock);
		setStockToAdd(0);
		document.body.style.overflow = "hidden";
	};

	const closeModal = (): void => {
		setSelectedGarment(null);
		setStockToAdd(0);
		setIsModalOpen(false);
		document.body.style.overflow = "";
	};

	function formatPrice(value: number): string {
		return new Intl.NumberFormat("pt-BR", {
			style: "currency",
			currency: "BRL",
		}).format(value);
	}

	async function handleUpdateStock() {
		if (!finalStock && finalStock !== 0) {
			setAlertMessage("Erro: Valor de estoque inválido");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (finalStock < 0) {
			setAlertMessage("Erro: O estoque não pode ser negativo");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		if (!selectedGarment) {
			setAlertMessage("Erro: Peça não selecionada");
			setAlertType("error");
			setIsAlertModalOpen(true);
			return;
		}

		try {
			const updatedGarment = {
				in_stock: finalStock,
			};

			await api.patch(`/garments/${selectedGarment.id}`, updatedGarment);

			const stockChange = finalStock - selectedGarment.in_stock;
			const changeText = stockChange > 0
				? `+${stockChange} unidades adicionadas`
				: stockChange < 0
					? `${Math.abs(stockChange)} unidades removidas`
					: 'Estoque mantido';

			setAlertMessage(`Estoque atualizado com sucesso!\n${changeText}\nNovo estoque: ${finalStock} unidades`);
			setAlertType("success");
			setAlertCallback(() => async () => {
				await reloadGarments();
				closeModal();
				setIsAlertModalOpen(false);
			});
			setIsAlertModalOpen(true);
		} catch (error: any) {
			console.error("Erro ao atualizar estoque:", error);
			let errorMessage = "Erro ao atualizar o estoque";

			if (error.response?.data?.error) {
				errorMessage = error.response.data.error;
			} else if (error.response?.data?.message) {
				errorMessage = error.response.data.message;
			}

			setAlertMessage(errorMessage);
			setAlertType("error");
			setIsAlertModalOpen(true);
		}
	}

	useEffect(() => {
		if (isModalOpen) {
			const focusableElements = document.querySelectorAll(
				'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])',
			);
			const firstElement = focusableElements[0] as HTMLElement;
			const lastElement = focusableElements[
				focusableElements.length - 1
			] as HTMLElement;

			const trapFocus = (e: KeyboardEvent) => {
				if (e.key === "Tab") {
					if (document.activeElement === lastElement && !e.shiftKey) {
						e.preventDefault();
						firstElement.focus();
					} else if (document.activeElement === firstElement && e.shiftKey) {
						e.preventDefault();
						lastElement.focus();
					}
				}
			};

			firstElement?.focus();
			document.addEventListener("keydown", trapFocus);

			return () => {
				document.removeEventListener("keydown", trapFocus);
			};
		}
	}, [isModalOpen]);

	useEffect(() => {
		reloadGarments();
	}, [reloadGarments]);

	return (
		<div className="min-h-screen flex flex-col">
			<Header pagename={"Controle de Estoque"} href={"/home"} $logout={false} />

			<main className="flex-grow flex flex-col items-center">
				<div className="flex gap-4 mt-4 w-2/3">
					<button
						type="button"
						onClick={() => setActiveTab("modelos")}
						className={`font-semibold flex-1 py-2 px-4 rounded-md transition-colors ${activeTab === "modelos"
							? "bg-blue-200 border border-gray-400"
							: "bg-gray-200 hover:bg-blue-100"
							}`}
					>
						Modelos
					</button>
					<button
						type="button"
						onClick={() => setActiveTab("materia-prima")}
						className={`font-semibold flex-1 py-2 px-4 rounded-md transition-colors ${activeTab === "materia-prima"
							? "bg-blue-200 border border-gray-400"
							: "bg-gray-200 hover:bg-blue-100"
							}`}
					>
						Matéria Prima
					</button>
				</div>

				<input
					type="text"
					placeholder={`Buscar por nome ${activeTab === "modelos" ? "do modelo" : "da matéria prima"}... (3 caracteres mínimo)`}
					value={searchTerm}
					onChange={(e) => setSearchTerm(e.target.value)}
					className="border border-gray-300 rounded-md h-10 mt-4 w-2/3 px-2 text-lg"
				/>

				<div
					id="stockItems"
					className="text-lg w-full overflow-auto place-items-center px-2 grid gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5 my-4"
				>
					{activeTab === "modelos" ? (
						garments.length > 0 ? (
							garments.map((garment) => (
								<div
									key={garment.id}
									className="stockItem relative flex flex-col w-72 py-5 px-8 items-center border border-gray-400 rounded text-center"
								>
									<p className="text-xl flex items-center gap-2">
										{garment.name}{" "}
										<Link to={"/lista-de-modelos"}>
											<PiNotePencilBold className="absolute top-3 right-4 cursor-pointer" />
										</Link>
									</p>

									<p>
										Cor: <span>{garment.color}</span>
									</p>
									<p>
										Ref:{" "}
										<span className="text-stone-500">#{garment.refcode}</span>
									</p>
									<p>
										Tamanho: <span>{garment.size}</span>
									</p>
									<p>
										Preço Unitário:{" "}
										<span>{formatPrice(Number(garment.price))}</span>
									</p>
									<p>
										Estoque: <span>{garment.in_stock}</span>
									</p>
									<p>
										Valor agregado em estoque:{" "}
										<span>
											{formatPrice(garment.in_stock * Number(garment.price))}
										</span>
									</p>

									<button
										type="button"
										className="border border-gray-400 rounded bg bg-blue-200 px-8 py-0.5 mt-2 font-roboto text-lg"
										onClick={() => {
											openModal(garment);
										}}
									>
										Modificar Estoque
									</button>
								</div>
							))
						) : (
							<div className="col-span-full text-center text-xl text-gray-500 py-8">
								{searchTerm.trim().length >= 3
									? `Nenhum modelo encontrado com o termo "${searchTerm}"`
									: "Nenhum modelo cadastrado"
								}
							</div>
						)
					) : (
						<div className="col-span-full text-center text-xl text-gray-500 py-8">
							Funcionalidade de Matéria Prima em desenvolvimento
						</div>
					)}

					{/* Modal */}
					{isModalOpen && (
						<div className="modal-overlay fixed inset-0 flex items-center justify-center backdrop-blur bg-black bg-opacity-50 px-1">
							<form
								className="modal-content bg-white p-5 rounded relative shadow-lg max-w-sm w-full"
								onSubmit={(e) => {
									e.preventDefault();
									handleUpdateStock();
								}}
							>
								<fieldset className="p-4 border border-gray-300 rounded-md flex flex-col gap-4">
									<IoClose
										onClick={closeModal}
										className="text-red-700 absolute top-7 right-7 cursor-pointer text-2xl"
									/>

									{/* Estoque Anterior */}
									<div className="input-wrapper text-md w-64 sm:w-full flex gap-2">
										Estoque Anterior:
										<p>{selectedStock}</p>
									</div>

									{/* Adicionar */}
									<div className="input-wrapper flex flex-col">
										<label htmlFor="add-stock" className="mb-1">
											Adicionar/Debitar:
										</label>
										<input
											type="number"
											id="add-stock"
											className="text-md border border-gray-300 rounded px-2 py-1 w-64 sm:w-full"
											placeholder="Adicione ou debite quantidade..."
											value={stockToAdd || ""}
											onChange={(e) => {
												const value = Number(e.target.value);
												if ((selectedStock || 0) + value >= 0) {
													setStockToAdd(value);
													setFinalStock((selectedStock || 0) + value);
												} else {
													setStockToAdd(-(selectedStock || 0));
													setFinalStock(0);
												}
											}}
										/>
									</div>

									{/* Estoque Atual */}
									<div className="input-wrapper flex flex-col">
										<label htmlFor="current-stock" className="mb-1">
											Estoque atual:
										</label>
										<input
											type="number"
											id="current-stock"
											className="text-md border border-gray-300 rounded px-2 py-1 w-64 sm:w-full"
											value={finalStock ?? ""}
											placeholder="Digite o estoque atualizado..."
											onChange={(e) => {
												const newStock = Number(e.target.value);
												if (newStock >= 0) {
													setFinalStock(newStock);
													setStockToAdd(newStock - (selectedStock || 0));
												} else {
													setFinalStock(0);
													setStockToAdd(-(selectedStock || 0));
												}
											}}
										/>
									</div>

									{/* Valor Adicionado/Debitado */}
									<div className="input-wrapper text-md w-64 sm:w-full flex gap-2">
										<p>
											Valor {stockToAdd > 0 ? "Adicionado" : "Debitado"}:{" "}
											<span
												className={`${stockToAdd < 0 ? "text-red-500" : "text-green-500"}`}
											>
												{stockToAdd}
											</span>
										</p>
									</div>

									{/* Botão de Confirmação */}
									<button
										type="submit"
										className="bg-blue-300 border border-gray-300 rounded px-4 py-2 mt-2"
									>
										Confirmar Atualização
									</button>
								</fieldset>
							</form>
						</div>
					)}
				</div>
			</main>

			<Footer />

			<AlertModal
				isOpen={isAlertModalOpen}
				onClose={() => {
					setIsAlertModalOpen(false);
					if (alertType === "success" && alertCallback) {
						alertCallback();
					}
				}}
				type={alertType}
				message={alertMessage}
				onConfirm={alertCallback ? alertCallback : undefined}
			/>
		</div>
	);
}
