import { Footer } from "../../components/Footer";
import { Header } from "../../components/Header";

import { useNavigate } from "react-router-dom";

import { useEffect, useState } from "react";
import { Link } from "react-router";
import { useAuth } from "../../hooks/auth";
import { api } from "../../service/api";

interface User {
	id: number;
	username: string;
	userType: "staff" | "admin";
	is_active: boolean;
	person: {
		id: number;
		fullname: string;
		contact: string;
		cpf: string | null;
		cnpj: string | null;
		personType: "individual" | "legal";
	};
	staff?: {
		id: number;
		comissionRate: string;
	};
}

export function Home() {
	const { signOut } = useAuth();
	const navigate = useNavigate();
	const [isUpdateLogOpen, setIsUpdateLogOpen] = useState(false);

	let user: User | null = null;

	const storedUser = localStorage.getItem("@managermalhas:user");

	if (storedUser) {
		try {
			user = JSON.parse(storedUser) as User;
		} catch (error) {
			console.error("Erro ao fazer o parse do usuário:", error);
		}
	}

	function handleSignOut() {
		signOut();
	}

	useEffect(() => {
		const checkUserExistence = async () => {
			const storedUser = localStorage.getItem("@managermalhas:user");

			if (storedUser) {
				try {
					const user = JSON.parse(storedUser);

					const response = await api.get(`/users/${user.id}`);

					if (!response.data) {
						throw new Error("Usuário não encontrado");
					}
				} catch (error) {
					console.error("Usuário não existe ou erro ao verificar:", error);
					signOut();
					navigate("/login");
				}
			}
		};

		checkUserExistence();
	}, [navigate, signOut]);

	return (
		<div className="flex flex-col min-h-screen">
			<Header pagename="Home" $logout={true} href={"/"} func={handleSignOut} />

			{/* Botão de Update Logs no topo direito - só aparece quando aba está fechada */}
			{!isUpdateLogOpen && (
				<button
					onClick={() => setIsUpdateLogOpen(true)}
					className="fixed top-28 right-4 z-40 bg-blue-200 hover:bg-blue-300 border border-gray-300 rounded p-2 hover:px-4 hover:py-2 shadow-lg transition-all duration-300 flex items-center gap-0 hover:gap-2 font-roboto group overflow-hidden"
					aria-label="Ver atualizações do sistema"
				>
					<svg
						className="w-5 h-5 flex-shrink-0"
						fill="none"
						stroke="currentColor"
						viewBox="0 0 24 24"
						xmlns="http://www.w3.org/2000/svg"
					>
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={2}
							d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
						/>
					</svg>
					<span className="max-w-0 group-hover:max-w-xs opacity-0 group-hover:opacity-100 transition-all duration-300 whitespace-nowrap overflow-hidden">
						Atualizações
					</span>
				</button>
			)}

			{/* Aba de Update Logs - só aparece quando aberta */}
			{isUpdateLogOpen && (
				<div
					className="fixed top-28 right-4 z-30 bg-white border border-gray-300 rounded-lg shadow-xl w-80 max-h-96 overflow-y-auto transition-all duration-300 ease-in-out"
					style={{ marginTop: "3rem" }}
				>
					<div className="p-4">
						<div className="flex justify-between items-center mb-4">
							<h3 className="text-lg font-bold text-gray-800 font-roboto">Update Logs</h3>
							<button
								onClick={() => setIsUpdateLogOpen(false)}
								className="text-gray-500 hover:text-gray-700 transition-colors"
								aria-label="Fechar update logs"
							>
								<svg
									className="w-5 h-5"
									fill="none"
									stroke="currentColor"
									viewBox="0 0 24 24"
								>
									<path
										strokeLinecap="round"
										strokeLinejoin="round"
										strokeWidth={2}
										d="M6 18L18 6M6 6l12 12"
									/>
								</svg>
							</button>
						</div>
						<div className="space-y-4 text-left">
							<div className="border-l-4 border-blue-500 pl-4">
								<h4 className="font-semibold text-gray-800 font-roboto">v2.1.0 - 16/08/2025</h4>
								<p className="text-sm text-gray-600 mt-1 font-inter">
									Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.
								</p>
							</div>
							<div className="border-l-4 border-green-500 pl-4">
								<h4 className="font-semibold text-gray-800 font-roboto">v2.0.5 - 10/08/2025</h4>
								<p className="text-sm text-gray-600 mt-1 font-inter">
									Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.
								</p>
							</div>
							<div className="border-l-4 border-yellow-500 pl-4">
								<h4 className="font-semibold text-gray-800 font-roboto">v2.0.0 - 01/08/2025</h4>
								<p className="text-sm text-gray-600 mt-1 font-inter">
									Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.
								</p>
							</div>
							<div className="border-l-4 border-purple-500 pl-4">
								<h4 className="font-semibold text-gray-800 font-roboto">v1.9.8 - 25/07/2025</h4>
								<p className="text-sm text-gray-600 mt-1 font-inter">
									Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.
								</p>
							</div>
						</div>
					</div>
				</div>
			)}

			<main className="flex-grow text-center font-bold">
				<h1 className="mt-6 mb-8 text-2xl">Bem-vindo ao sistema de Malhas</h1>

				<div
					id="btn-box"
					className="grid gap-2 sm:grid-cols-2 lg:grid-cols-3 mb-3"
				>
					<Link
						to={"/novo-pedido"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Novo Pedido</p>
					</Link>
					<Link
						to={"/novo-cliente"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Novo Cliente</p>
					</Link>
					<Link
						to={"/novo-modelo"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Cadastrar Modelo</p>
					</Link>
					<Link
						to={"/lista-de-pedidos"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Pedidos</p>
					</Link>
					<Link
						to={"/lista-de-clientes"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Clientes</p>
					</Link>
					<Link
						to={"/lista-de-modelos"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Lista de Modelos</p>
					</Link>
					<Link
						to={"/nova-materia-prima"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Nova Matéria-Prima</p>
					</Link>
					<Link
						to={"/estoque"}
						type="button"
						className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
					>
						<p>Controle de Estoque</p>
					</Link>
					{user?.userType === "admin" && (
						<Link
							to={"/area-administrativa"}
							type="button"
							className="bg-gray-300 py-20 px-0 text-lg font-roboto mx-8"
						>
							<p>Área de Administração</p>
						</Link>
					)}
				</div>
			</main>

			{/* Botão flutuante do WhatsApp */}
			<a
				href="https://wa.me/5535999602858?text=Ol%C3%A1.%0AEstou%20precisando%20de%20suporte%20no%20sistema..."
				target="_blank"
				rel="noopener noreferrer"
				className="fixed bottom-20 right-6 z-50 bg-green-500 hover:bg-green-600 text-white rounded-full p-4 shadow-lg transition-all duration-300 hover:scale-110"
				aria-label="Contato via WhatsApp"
			>
				<svg
					className="w-6 h-6"
					fill="currentColor"
					viewBox="0 0 24 24"
					xmlns="http://www.w3.org/2000/svg"
				>
					<path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.893 3.488" />
				</svg>
			</a>

			<Footer />
		</div>
	);
}
